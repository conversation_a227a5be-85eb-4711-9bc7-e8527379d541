# Archon Frontend Environment Variables
# Copy this file to .env and fill in your values

# API Configuration
VITE_API_URL=http://localhost:8000
VITE_WS_API_URL=ws://localhost:8000

# Application Configuration
VITE_APP_TITLE=Archon Code Analyzer
VITE_APP_BASE_PATH=/

# Authentication Configuration
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_firebase_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef123456

# Stack Auth Configuration (alternative to Firebase)
VITE_STACK_AUTH_PROJECT_ID=your_stack_auth_project_id
VITE_STACK_AUTH_PUBLISHABLE_CLIENT_KEY=your_stack_auth_publishable_key

# Development Configuration
VITE_DEV_MODE=true
VITE_DEBUG=true

# GitHub OAuth (if using direct GitHub auth)
VITE_GITHUB_CLIENT_ID=your_github_client_id

# Databutton Extensions (JSON format)
# VITE_DATABUTTON_EXTENSIONS=[{"name":"firebase-auth","version":"1.0.0","config":{"projectId":"your_project"}}]
